package com.controller;

import com.response.Response;
import com.util.UploadFile;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/common")
public class CommonController {


	/*
	 * 上传图片
	 *
	 * @param req
	 * @param res
	 * @throws ServletException
	 * @throws IOException
	 */
	@RequestMapping("/uploadFile")
	@CrossOrigin
	public Response<UploadFile> upload(HttpServletRequest req, HttpServletResponse res, @RequestParam("file") MultipartFile file) throws ServletException, IOException {
		String fileName = file.getOriginalFilename();
		fileName = fileName.substring(fileName.lastIndexOf("."));
		String fname = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + fileName;
		String basePath = req.getSession().getServletContext().getRealPath("/upload");
		File dir = new File(basePath);
		if (!dir.exists()) {
			dir.mkdir();
		}
		System.out.println("basePath = " + basePath);
		//String furl = "upload/" + fname;
		String furl =  fname;
		String url = req.getScheme() + "://" + req.getServerName() + ":" + req.getServerPort() + req.getContextPath() + "/" + furl;
		File dest = new File(basePath, fname);
		try {
			file.transferTo(dest);
			UploadFile uploadFile = new UploadFile();
			uploadFile.setFilePath(furl);
			uploadFile.setUrl(url);
			return Response.success(uploadFile);
		} catch (IOException e) {
			return Response.error();
		}
	}
	/*
	 * 上传图片 - 小程序专用版本 (避免Jackson依赖问题)
	 *
	 * @param req
	 * @param res
	 * @throws ServletException
	 * @throws IOException
	 */
	@RequestMapping("/uploadFile2")
	@CrossOrigin
	public void uploadFile2(HttpServletRequest req, HttpServletResponse res, @RequestParam("file") MultipartFile file) throws ServletException, IOException {
		res.setContentType("application/json;charset=UTF-8");
		res.setCharacterEncoding("UTF-8");

		try {
			// 验证文件
			if (file == null || file.isEmpty()) {
				res.getWriter().write("{\"code\":400,\"message\":\"文件不能为空\"}");
				return;
			}

			String fileName = file.getOriginalFilename();
			if (fileName == null || fileName.trim().isEmpty()) {
				res.getWriter().write("{\"code\":400,\"message\":\"文件名不能为空\"}");
				return;
			}

			// 验证文件类型 (只允许图片)
			String fileExtension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
			if (!fileExtension.matches("\\.(jpg|jpeg|png|gif|bmp|webp)$")) {
				res.getWriter().write("{\"code\":400,\"message\":\"只支持图片格式文件\"}");
				return;
			}

			// 验证文件大小 (限制5MB)
			if (file.getSize() > 5 * 1024 * 1024) {
				res.getWriter().write("{\"code\":400,\"message\":\"文件大小不能超过5MB\"}");
				return;
			}

			// 生成唯一文件名
			String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
			String uniqueFileName = timestamp + "_" + System.nanoTime() + fileExtension;

			// 创建上传目录
			String basePath = req.getSession().getServletContext().getRealPath("/upload");
			File uploadDir = new File(basePath);
			if (!uploadDir.exists()) {
				uploadDir.mkdirs();
			}

			// 保存文件
			File destFile = new File(uploadDir, uniqueFileName);
			file.transferTo(destFile);

			// 构建访问URL
			String fileUrl = req.getScheme() + "://" + req.getServerName() + ":" + req.getServerPort()
							+ req.getContextPath() + "/upload/" + uniqueFileName;

			// 返回成功响应
			String jsonResponse = String.format(
				"{\"code\":200,\"message\":\"上传成功\",\"data\":{\"filePath\":\"%s\",\"url\":\"%s\",\"fileName\":\"%s\",\"fileSize\":%d}}",
				uniqueFileName, fileUrl, fileName, file.getSize()
			);
			res.getWriter().write(jsonResponse);

		} catch (Exception e) {
			e.printStackTrace();
			res.getWriter().write("{\"code\":500,\"message\":\"上传失败: " + e.getMessage() + "\"}");
		}
	}

	@RequestMapping("/downFile")
	@CrossOrigin
	public  void downFile(HttpServletRequest req,HttpServletResponse res){
		String filePath = req.getParameter("filePath");
		String basePath = req.getSession().getServletContext().getRealPath("/upload");
		String fileName = basePath + filePath;
		res.setHeader("content-type", "application/octet-stream");
		res.setContentType("application/octet-stream");
		res.setHeader("Content-Disposition", "attachment;filename=" + filePath);
		byte[] buff = new byte[1024];
		BufferedInputStream bis = null;
		OutputStream os = null;
		try {
			os = res.getOutputStream();
			bis = new BufferedInputStream(new FileInputStream(new File(fileName)));
			int i = bis.read(buff);
			while (i != -1) {
				os.write(buff, 0, buff.length);
				os.flush();
				i = bis.read(buff);
			}
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (bis != null) {
				try {
					bis.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		System.out.println("success");
	}

}

