package com.util;

import java.io.UnsupportedEncodingException;

/**
 * 字符编码处理工具类
 * 专门用于解决小程序端中文乱码问题
 */
public class EncodingUtil {
    
    /**
     * 将ISO-8859-1编码的字符串转换为UTF-8编码
     * 主要用于处理小程序端传递的中文参数
     * 
     * @param str 需要转换的字符串
     * @return 转换后的UTF-8字符串
     */
    public static String convertToUTF8(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }
        
        try {
            // 检查字符串是否已经是UTF-8编码
            if (isUTF8(str)) {
                return str;
            }
            
            // 将ISO-8859-1编码转换为UTF-8编码
            return new String(str.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return str; // 如果转换失败，返回原字符串
        }
    }
    
    /**
     * 检查字符串是否为UTF-8编码
     * 
     * @param str 待检查的字符串
     * @return true表示是UTF-8编码，false表示不是
     */
    private static boolean isUTF8(String str) {
        try {
            // 如果字符串能够正确地从UTF-8编码转换回来，说明它本身就是UTF-8编码
            byte[] bytes = str.getBytes("UTF-8");
            String converted = new String(bytes, "UTF-8");
            return str.equals(converted);
        } catch (UnsupportedEncodingException e) {
            return false;
        }
    }
    
    /**
     * 批量转换字符串数组的编码
     * 
     * @param strings 字符串数组
     * @return 转换后的字符串数组
     */
    public static String[] convertArrayToUTF8(String[] strings) {
        if (strings == null) {
            return null;
        }
        
        String[] result = new String[strings.length];
        for (int i = 0; i < strings.length; i++) {
            result[i] = convertToUTF8(strings[i]);
        }
        return result;
    }
    
    /**
     * 安全的字符串编码转换
     * 如果转换失败，返回默认值
     * 
     * @param str 需要转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的字符串或默认值
     */
    public static String safeConvertToUTF8(String str, String defaultValue) {
        try {
            String result = convertToUTF8(str);
            return result != null ? result : defaultValue;
        } catch (Exception e) {
            e.printStackTrace();
            return defaultValue;
        }
    }
}
